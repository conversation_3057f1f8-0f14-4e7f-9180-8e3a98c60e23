import React, { useEffect, useState } from 'react';
import { Search, Download, Eye, Trash, } from 'lucide-react';
import Input from '@/components/ui/Input';
import Form from './Form';
import EditForm from './EditForm';
import { useCollection } from '@/hooks/useCollection';

export default function MobileTable({ serviceName = '' }) {
  const { data, deleteItem } = useCollection('cfs_service_details', {
    expand: 'order,jobOrder,container,type'
  });

  const [filteredData, setFilteredData] = useState([]);
  const [filteredRequests, setFilteredRequests] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    if (data?.length > 0) {
      setFilteredData(data.filter((item) => item?.expand?.type?.title === serviceName))
    }
  }, [data]);


  const getStatusClass = (status) => {
    switch (status) {
      case 'Completed':
        return 'bg-green-100 text-green-800 border border-green-500';
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800 border border-yellow-500';
      case 'Cancelled':
        return 'bg-red-100 text-red-800 border border-red-500';
      default:
        return 'bg-gray-300 text-gray-800 border border-gray-800';
    }
  };

  // const filteredRequests = filteredData.filter(request => {
  //   const matchesSearch =
  //     request?.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
  //     request?.expand?.container?.containerNo.toLowerCase().includes(searchQuery.toLowerCase()) ||
  //     request?.order.toLowerCase().includes(searchQuery.toLowerCase());
  //   request?.jobOrder.toLowerCase().includes(searchQuery.toLowerCase());
  //   return matchesSearch;
  // });

  useEffect(() => {
    setFilteredRequests(filteredData);
  }, [data, filteredData, searchQuery])


  return (
    <div className="border rounded-xl bg-accent flex flex-col p-4">
      <div className="flex-1 overflow-y-auto">
        <h2 className="text-xl font-semibold text-foreground mb-4">{serviceName} List</h2>
        <div className="flex justify-end items-center my-4">
          <Form />
        </div>

        <div className="px-4 py-8 flex items-center justify-between">
          <div className="relative flex-1 mr-2">
            <Input
              type="text"
              placeholder="Search by ID, Order Id, Job Order Id or Container No."
              className="pl-8 w-full bg-accent text-xs"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Search size={16} className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>
        </div>

        <div className="px-4 pb-4">
          {filteredData.map((request, index) => (
            <div key={index} className="border rounded-lg p-3 mb-3 shadow-sm">
              <div className="flex justify-between items-center mb-4">
                <span className={`text-xs px-2 py-1 rounded-full ${getStatusClass(request?.status)}`}>
                  {request?.status}
                </span>
              </div>
              <h1 className="font-medium"># {request?.id}</h1>
              <p className="text-sm text-gray-600 mb-1">Order: {request?.order}</p>
              <p className="text-sm text-gray-600 mb-1">Job Order: {request?.jobOrder}</p>
              <p className="text-sm text-gray-600 mb-1">
                Date: {
                  new Date(request?.date)?.toLocaleDateString('en-US', {
                    day: 'numeric',
                    month: 'short',
                    year: 'numeric',
                  })
                }
              </p>
              <p className="text-sm text-gray-600 mb-1">Agent: {request?.agent}</p>
              {
                request?.receiptNo && (
                  <p className="text-sm text-gray-600 mb-1">Receipt No: {request?.type}</p>
                )
              }
              {
                request?.remarks && (
                  <p className="text-sm text-gray-600 mb-1">Remarks: {request.remarks}</p>
                )
              }
              <div className="flex justify-end items-center pt-4">
                <div className='flex gap-2 items-center'>
                  <Eye
                    size={18}
                    className="cursor-pointer text-primary"
                    onClick={() => console.log('View details for', row.original.id)}
                  />
                  <EditForm info={request} />
                  <Trash
                    size={18}
                    className="cursor-pointer text-primary"
                    onClick={async () => {
                      console.log('Delete details for', row.original.id);
                      const confirmation = confirm('Are you sure you want to delete this entry?');
                      if (confirmation) {
                        await deleteItem(request?.id);
                      }
                    }}
                  />
                  <Download
                    size={18}
                    className="cursor-pointer text-primary"
                    onClick={() => console.log('Download files for', row.original.id)}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div >
  );
}

