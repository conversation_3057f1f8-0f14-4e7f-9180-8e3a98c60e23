"use client";
import { useEffect } from "react";
import { useSidebar } from "@/contexts/SidebarProvider";
import Stats from "./components/Stats";
import { Menu, Package, Plus, Loader2 } from "lucide-react";
import { DataTable } from "@/components/ui/Table";
import { dashboardCols } from "./components/columns";
import { useIsMobile } from "@/hooks/use-mobile";
import MobileTable from "./components/MobileTable";
import { useDashboardData } from "@/hooks/useDashboardData";
import { transformOrderData } from "@/utils/dashboardHelpers";

export default function CustomerDashboardPage() {
  const { setTitle } = useSidebar();
  const { data, loading, error } = useDashboardData();
  const isMobile = useIsMobile();

  useEffect(() => {
    setTitle("Dashboard");
  }, [setTitle]);

  // Transform orders data for display
  const transformedOrders = transformOrderData(data.orders);

  console.log("Data",data);
  

  if (loading) {
    return (
      <section className="grid gap-8">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading dashboard data...</span>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="grid gap-8">
        <div className="p-6 rounded-xl shadow-2xl bg-red-50 border border-red-200">
          <h2 className="text-lg font-semibold text-red-800 mb-2">Error Loading Dashboard</h2>
          <p className="text-red-600">
            {error.message || 'Failed to load dashboard data. Please try refreshing the page.'}
          </p>
        </div>
      </section>
    );
  }

  return (
    <section className="grid gap-8">
      <Stats
        orders={data.orders}
        serviceRequests={data.serviceRequests}
        jobOrders={data.jobOrders}
      />

      {/* <div className="grid lg:grid-cols-3 md:grid-cols-2 grid-cols-1 w-full gap-6"> */}
      {/*   <button */}
      {/*     className="flex items-center justify-center gap-3 p-4 rounded-md bg-blue-600 text-white hover:bg-blue-700 transition-colors md:w-full w-[300px]" */}
      {/*   > */}
      {/*     <Plus className="h-5 w-5" /> */}
      {/*     <span>Create Job Order</span> */}
      {/*   </button> */}
      {/*   <button */}
      {/*     className="flex items-center justify-center gap-3 p-4 rounded-md bg-green-600 text-white hover:bg-green-700 transition-colors md:w-full w-[300px]" */}
      {/*   > */}
      {/*     <Menu className="h-5 w-5" /> */}
      {/*     <span>View All Requests</span> */}
      {/*   </button> */}
      {/*   <button */}
      {/*     className="flex items-center justify-center gap-3 p-4 rounded-md bg-purple-600 text-white hover:bg-purple-700 transition-colors md:w-full w-[300px]" */}
      {/*   > */}
      {/*     <Package className="h-5 w-5" /> */}
      {/*     <span>Orders Page</span> */}
      {/*   </button> */}
      {/* </div> */}

      <div className="p-6 rounded-xl shadow-2xl bg-[var(--accent)]">
        <h1 className="text-xl font-semibold mb-4">Recent Orders</h1>
        {transformedOrders.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No orders found</p>
          </div>
        ) : isMobile ? (
          <MobileTable orders={transformedOrders.slice(0, 5)} />
        ) : (
          <DataTable
            data={transformedOrders.slice(0, 5)}
            columns={dashboardCols}
            displayButtons={false}
            displayFilters={false}
          />
        )}
      </div>
    </section>
  );
}
