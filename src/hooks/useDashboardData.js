import { useState, useEffect, useCallback } from 'react';
import pbclient from '@/lib/db';
import { useAuth } from '@/contexts/AuthContext';

export function useDashboardData() {
  const { user } = useAuth();
  const [data, setData] = useState({
    orders: [],
    jobOrders: [],
    serviceRequests: [],
    pricingRequests: [],
    containers: [],
    serviceProviders: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchDashboardData = useCallback(async () => {
    if (!user) {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Fetch data based on user role with proper error handling for each collection
      const fetchCollection = async (collectionName, options = {}) => {
        try {
          return await pbclient.collection(collectionName).getFullList(options);
        } catch (err) {
          console.warn(`Failed to fetch ${collectionName}:`, err);
          return [];
        }
      };

      let orders = [];
      let jobOrders = [];
      let serviceRequests = [];
      let pricingRequests = [];

      if (user.role === 'Customer') {
        // For customers, fetch their orders and related data
        orders = await fetchCollection('cfs_orders', {
          filter: `customer = "${user.id}"`,
          expand: 'cfs,customer,containers,createdBy',
          sort: '-created'
        });

        serviceRequests = await fetchCollection('cfs_service_requests', {
          filter: `user = "${user.id}"`,
          expand: 'order,serviceType',
          sort: '-created'
        });

        pricingRequests = await fetchCollection('cfs_pricing_request', {
          filter: `user = "${user.id}"`,
          expand: 'serviceProvider',
          sort: '-created'
        });
      } else if (user.role === 'Merchant') {
        // For merchants/clients, fetch orders they created or are assigned to
        orders = await fetchCollection('cfs_orders', {
          filter: `createdBy = "${user.id}"`,
          expand: 'cfs,customer,containers,createdBy',
          sort: '-created'
        });

        jobOrders = await fetchCollection('cfs_job_order', {
          filter: `createdBy = "${user.id}"`,
          expand: 'order,serviceType,containers',
          sort: '-created'
        });

        serviceRequests = await fetchCollection('cfs_service_requests', {
          expand: 'user,order,serviceType',
          sort: '-created'
        });
      } else {
        // For GOL staff and admins, fetch all data
        orders = await fetchCollection('cfs_orders', {
          expand: 'cfs,customer,containers,createdBy',
          sort: '-created'
        });

        jobOrders = await fetchCollection('cfs_job_order', {
          expand: 'order,serviceType,containers',
          sort: '-created'
        });

        serviceRequests = await fetchCollection('cfs_service_requests', {
          expand: 'user,order,serviceType',
          sort: '-created'
        });

        pricingRequests = await fetchCollection('cfs_pricing_request', {
          expand: 'user,serviceProvider',
          sort: '-created'
        });
      }

      // Always fetch containers and service providers for reference
      const containers = await fetchCollection('containers', {
        expand: 'ownedBy',
        sort: '-created'
      });

      const serviceProviders = await fetchCollection('service_provider', {
        expand: 'service,author',
        sort: '-created'
      });

      setData({
        orders,
        jobOrders,
        serviceRequests,
        pricingRequests,
        containers,
        serviceProviders
      });

    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError(err);
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  const refetch = useCallback(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  return {
    data,
    loading,
    error,
    refetch
  };
}
