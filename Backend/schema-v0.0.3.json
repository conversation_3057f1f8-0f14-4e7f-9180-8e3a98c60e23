[{"id": "_pb_users_auth_", "listRule": "id = @request.auth.id", "viewRule": "id = @request.auth.id", "createRule": "", "updateRule": "id = @request.auth.id", "deleteRule": "id = @request.auth.id", "name": "users", "type": "auth", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cost": 0, "hidden": true, "id": "password901924565", "max": 0, "min": 8, "name": "password", "pattern": "", "presentable": false, "required": true, "system": true, "type": "password"}, {"autogeneratePattern": "[a-zA-Z0-9]{50}", "hidden": true, "id": "text2504183744", "max": 60, "min": 30, "name": "<PERSON><PERSON><PERSON>", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": true, "type": "text"}, {"exceptDomains": null, "hidden": false, "id": "email3885137012", "name": "email", "onlyDomains": null, "presentable": false, "required": true, "system": true, "type": "email"}, {"hidden": false, "id": "bool1547992806", "name": "emailVisibility", "presentable": false, "required": false, "system": true, "type": "bool"}, {"hidden": false, "id": "bool256245529", "name": "verified", "presentable": false, "required": false, "system": true, "type": "bool"}, {"autogeneratePattern": "", "hidden": false, "id": "text1579384326", "max": 255, "min": 0, "name": "name", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file376926767", "maxSelect": 1, "maxSize": 0, "mimeTypes": ["image/jpeg", "image/png", "image/svg+xml", "image/gif", "image/webp"], "name": "avatar", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": null, "type": "file"}, {"autogeneratePattern": "", "hidden": false, "id": "text2208304744", "max": 0, "min": 0, "name": "firstname", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text824489398", "max": 0, "min": 0, "name": "lastname", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text4166911607", "max": 0, "min": 0, "name": "username", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select1466534506", "maxSelect": 1, "name": "role", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Root", "GOLMod", "GOLStaff", "Merchant", "Customer"]}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": ["CREATE UNIQUE INDEX `idx_tokenKey__pb_users_auth_` ON `users` (`tokenKey`)", "CREATE UNIQUE INDEX `idx_email__pb_users_auth_` ON `users` (`email`) WHERE `email` != ''", "CREATE UNIQUE INDEX `idx_1spF9e9g0x` ON `users` (`username`)"], "system": false, "authRule": "", "manageRule": null, "authAlert": {"enabled": true, "emailTemplate": {"subject": "Login from a new location", "body": "<p>Hello,</p>\n<p>We noticed a login to your {APP_NAME} account from a new location.</p>\n<p>If this was you, you may disregard this email.</p>\n<p><strong>If this wasn't you, you should immediately change your {APP_NAME} account password to revoke access from all other locations.</strong></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}}, "oauth2": {"mappedFields": {"id": "", "name": "name", "username": "", "avatarURL": "avatar"}, "enabled": false}, "passwordAuth": {"enabled": true, "identityFields": ["email", "username"]}, "mfa": {"enabled": false, "duration": 1800, "rule": ""}, "otp": {"enabled": false, "duration": 180, "length": 8, "emailTemplate": {"subject": "OTP for {APP_NAME}", "body": "<p>Hello,</p>\n<p>Your one-time password is: <strong>{OTP}</strong></p>\n<p><i>If you didn't ask for the one-time password, you can ignore this email.</i></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}}, "authToken": {"duration": 604800}, "passwordResetToken": {"duration": 1800}, "emailChangeToken": {"duration": 1800}, "verificationToken": {"duration": 259200}, "fileToken": {"duration": 180}, "verificationTemplate": {"subject": "Verify your {APP_NAME} email", "body": "<p>Hello,</p>\n<p>Thank you for joining us at {APP_NAME}.</p>\n<p>Click on the button below to verify your email address.</p>\n<p>\n  <a class=\"btn\" href=\"{APP_URL}/_/#/auth/confirm-verification/{TOKEN}\" target=\"_blank\" rel=\"noopener\">Verify</a>\n</p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}, "resetPasswordTemplate": {"subject": "Reset your {APP_NAME} password", "body": "<p>Hello,</p>\n<p>Click on the button below to reset your password.</p>\n<p>\n  <a class=\"btn\" href=\"{APP_URL}/_/#/auth/confirm-password-reset/{TOKEN}\" target=\"_blank\" rel=\"noopener\">Reset password</a>\n</p>\n<p><i>If you didn't ask to reset your password, you can ignore this email.</i></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}, "confirmEmailChangeTemplate": {"subject": "Confirm your {APP_NAME} new email address", "body": "<p>Hello,</p>\n<p>Click on the button below to confirm your new email address.</p>\n<p>\n  <a class=\"btn\" href=\"{APP_URL}/_/#/auth/confirm-email-change/{TOKEN}\" target=\"_blank\" rel=\"noopener\">Confirm new email</a>\n</p>\n<p><i>If you didn't ask to change your email address, you can ignore this email.</i></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}}, {"id": "pbc_3222384912", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "allowed_service_providers", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_863811952", "hidden": false, "id": "relation3785202386", "maxSelect": 999, "minSelect": 0, "name": "service", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_3190968249", "hidden": false, "id": "relation2462348188", "maxSelect": 1, "minSelect": 0, "name": "provider", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2375276105", "maxSelect": 1, "minSelect": 0, "name": "user", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2947930106", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "cfs_job_order", "type": "base", "fields": [{"autogeneratePattern": "JOB-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "JOB-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_2872855771", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date3149528700", "max": "", "min": "", "name": "fromDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "date301681072", "max": "", "min": "", "name": "toDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"cascadeDelete": false, "collectionId": "pbc_2651147062", "hidden": false, "id": "relation1138201242", "maxSelect": 1, "minSelect": 0, "name": "serviceType", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_1864144027", "hidden": false, "id": "relation2444010507", "maxSelect": 999, "minSelect": 0, "name": "containers", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3545646658", "maxSelect": 1, "minSelect": 0, "name": "created<PERSON>y", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_1015806038", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "cfs_order_movement", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_2872855771", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date2862495610", "max": "", "min": "", "name": "date", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"autogeneratePattern": "", "hidden": false, "id": "text2063623452", "max": 0, "min": 0, "name": "status", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2872855771", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "cfs_orders", "type": "base", "fields": [{"autogeneratePattern": "ORD-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "ORD-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text118057898", "max": 0, "min": 0, "name": "igmNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1112657042", "max": 0, "min": 0, "name": "blNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text3145606124", "max": 0, "min": 0, "name": "boeNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_1864144027", "hidden": false, "id": "relation2444010507", "maxSelect": 999, "minSelect": 0, "name": "containers", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text3733864923", "max": 0, "min": 0, "name": "consignee<PERSON><PERSON>", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text3096597736", "max": 0, "min": 0, "name": "cha<PERSON>ame", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_3190968249", "hidden": false, "id": "relation1333477580", "maxSelect": 1, "minSelect": 0, "name": "cfs", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2168032777", "maxSelect": 1, "minSelect": 0, "name": "customer", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date3149528700", "max": "", "min": "", "name": "fromDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "date301681072", "max": "", "min": "", "name": "toDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text3429085233", "max": 0, "min": 0, "name": "orderDescription", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3545646658", "maxSelect": 1, "minSelect": 0, "name": "created<PERSON>y", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 100000000, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "bool1531251113", "name": "merchantVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2891322017", "maxSelect": 1, "minSelect": 0, "name": "merchantVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool465722684", "name": "golVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3429284244", "maxSelect": 1, "minSelect": 0, "name": "golVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": ["CREATE UNIQUE INDEX `idx_uLjPoop6Vx` ON `cfs_orders` (\n  `igmNo`,\n  `blNo`,\n  `boeNo`\n)"], "system": false}, {"id": "pbc_3891445662", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "cfs_pricing_request", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2375276105", "maxSelect": 1, "minSelect": 0, "name": "user", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_3190968249", "hidden": false, "id": "relation3497959979", "maxSelect": 1, "minSelect": 0, "name": "serviceProvider", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "select2363381545", "maxSelect": 1, "name": "type", "presentable": false, "required": false, "system": false, "type": "select", "values": ["<PERSON><PERSON>", "Normal"]}, {"hidden": false, "id": "bool465722684", "name": "golVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3429284244", "maxSelect": 1, "minSelect": 0, "name": "golVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "number1885463975", "max": null, "min": null, "name": "preferableRate", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number2022905682", "max": null, "min": null, "name": "noOfContainers", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number3809315573", "max": null, "min": null, "name": "avgContainerSize", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number1241718566", "max": null, "min": null, "name": "containersPerMonth", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2688346109", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "cfs_service_details", "type": "base", "fields": [{"autogeneratePattern": "REC-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "REC-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_2872855771", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2947930106", "hidden": false, "id": "relation3993090758", "maxSelect": 1, "minSelect": 0, "name": "jobOrder", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_1864144027", "hidden": false, "id": "relation3349343259", "maxSelect": 1, "minSelect": 0, "name": "container", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2651147062", "hidden": false, "id": "relation2363381545", "maxSelect": 1, "minSelect": 0, "name": "type", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text646683805", "max": 0, "min": 0, "name": "agent", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "date2862495610", "max": "", "min": "", "name": "date", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text1562571485", "max": 0, "min": 0, "name": "receiptNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_3040225314", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "cfs_service_requests", "type": "base", "fields": [{"autogeneratePattern": "REQ-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "REQ-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2375276105", "maxSelect": 1, "minSelect": 0, "name": "user", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2872855771", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2651147062", "hidden": false, "id": "relation1138201242", "maxSelect": 1, "minSelect": 0, "name": "serviceType", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool1531251113", "name": "merchantVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2891322017", "maxSelect": 1, "minSelect": 0, "name": "merchantVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "bool465722684", "name": "golVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3429284244", "maxSelect": 1, "minSelect": 0, "name": "golVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text3282145173", "max": 0, "min": 0, "name": "customerRemarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text356646161", "max": 0, "min": 0, "name": "clientReason", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_3215430941", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "cfs_tariffs_request", "type": "base", "fields": [{"autogeneratePattern": "REQ-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "REQ-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_2872855771", "hidden": false, "id": "relation4113142680", "maxSelect": 1, "minSelect": 0, "name": "order", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_1864144027", "hidden": false, "id": "relation3349343259", "maxSelect": 1, "minSelect": 0, "name": "container", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "select2363381545", "maxSelect": 1, "name": "type", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Loaded", "<PERSON><PERSON><PERSON>"]}, {"hidden": false, "id": "date3149528700", "max": "", "min": "", "name": "fromDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "date301681072", "max": "", "min": "", "name": "toDate", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text1156222427", "max": 0, "min": 0, "name": "remarks", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Pending", "Accepted", "Rejected", "In Progress", "Completed"]}, {"hidden": false, "id": "bool1531251113", "name": "merchantVerified", "presentable": false, "required": false, "system": false, "type": "bool"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2891322017", "maxSelect": 1, "minSelect": 0, "name": "merchantVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "bool465722684", "maxSelect": 1, "minSelect": 0, "name": "golVerifiedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_1864144027", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "containers", "type": "base", "fields": [{"autogeneratePattern": "CON-[0-9]{11}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "CON-[0-9]{3,11}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2013763573", "maxSelect": 1, "minSelect": 0, "name": "ownedBy", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text4070878934", "max": 0, "min": 0, "name": "containerNo", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text4156564586", "max": 0, "min": 0, "name": "size", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["Good", "Empty", "Loading", "Loaded", "Damaged", "Missing", "Broken", "COR", "Free"]}, {"autogeneratePattern": "", "hidden": false, "id": "text3882496157", "max": 0, "min": 0, "name": "cargoType", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": ["CREATE UNIQUE INDEX `idx_f7G3fmcFWM` ON `containers` (`containerNo`)"], "system": false}, {"id": "pbc_3190968249", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "service_provider", "type": "base", "fields": [{"autogeneratePattern": "SP-[0-9]{12}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "SP-[0-9]{2,12}", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_863811952", "hidden": false, "id": "relation3785202386", "maxSelect": 999, "minSelect": 0, "name": "service", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990059", "max": 0, "min": 0, "name": "title", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1843675174", "max": 0, "min": 0, "name": "description", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file104153177", "maxSelect": 99, "maxSize": 0, "mimeTypes": [], "name": "files", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"autogeneratePattern": "", "hidden": false, "id": "text1587448267", "max": 0, "min": 0, "name": "location", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "json3217087507", "maxSize": 0, "name": "features", "presentable": false, "required": false, "system": false, "type": "json"}, {"autogeneratePattern": "", "hidden": false, "id": "text1281549880", "max": 0, "min": 0, "name": "contact", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "number2136230317", "max": null, "min": null, "name": "tariffRates", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number1797807559", "max": null, "min": null, "name": "freeDays", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "number3371656666", "max": null, "min": null, "name": "monthlyDues", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "json1874629670", "maxSize": 0, "name": "tags", "presentable": false, "required": false, "system": false, "type": "json"}, {"hidden": false, "id": "number3632866850", "max": null, "min": null, "name": "rating", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation3182418120", "maxSelect": 1, "minSelect": 0, "name": "author", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_863811952", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "services", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990059", "max": 0, "min": 0, "name": "title", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1843675174", "max": 0, "min": 0, "name": "description", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2651147062", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "sub_services", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_863811952", "hidden": false, "id": "relation3785202386", "maxSelect": 1, "minSelect": 0, "name": "service", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text724990059", "max": 0, "min": 0, "name": "title", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text1843675174", "max": 0, "min": 0, "name": "description", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": ["CREATE UNIQUE INDEX `idx_bwXerwFYZd` ON `sub_services` (`title`)"], "system": false}]